package com.yutoudev.irrigation.irr.service.iot.tools;

import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
public class CalcDateTimeHelper {

    public static LocalDate getCalcDate(LocalDateTime time) {
        return time.minusHours(8).minusMinutes(1).toLocalDate();
    }

    public Pair<LocalDateTime, LocalDateTime> getCalcHourRange(LocalDateTime time) {
        LocalDateTime calcDate = getCalcDate(time).atStartOfDay();
        return Pair.of(calcDate.withHour(8).withMinute(0), calcDate.plusDays(1).withHour(8).withMinute(0));
    }
}
