package com.yutoudev.irrigation.irr.service.waterdatacalc;

import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 水情相关数据计算
 *
 * <AUTHOR>
 */
public interface ReservoirDataCalcService {

    /**
     * 获取主水源库容
     *
     * @param equip 设备
     * @param waterLevel 水库水位
     * @return 主水源库
     */
    double getMainWaterCapacityByWaterLevel(EquipBaseCacheVO equip, double waterLevel);

    /**
     * 获取主水源库容差
     *
     * @param equip 设备
     * @param waterLevel 水库水位
     * @return 主水源库容差
     */
    double getMainWaterCapacityDiff(EquipBaseCacheVO equip, double waterLevel);

    /**
     * 获取出水和出水量
     *
     * @param time 时间
     * @return 出水和出水量
     */
    Pair<Double, Double> getOutFlowAndVolume(LocalDateTime time);

    /**
     * 获取入库流量和入库水量、库容差
     *
     * @param time 时间
     * @return 入水和入水量
     */
    Triple<Double, Double, Double> getInFlowAndVolume(LocalDateTime time);

    /**
     * 根据主水源库水位计算主水源可供水量
     *
     * @param waterLevel 主水源库容
     * @return 可用水量
     */
    double getMainWaterSupplyVolumeByWaterLevel(double waterLevel);

    /**
     * 根据主水源库容量计算主水源可供水量
     *
     * @param storageCapacity 主水源库容
     * @return 可用水量
     */
    double getMainWaterSupplyVolumeByStorageCapacity(double storageCapacity);

    /**
     * 获取今日主水源可供水量
     *
     * @return 可供水量
     */
    double getMainWaterSupplyVolumeByToday();

    /**
     * 获取主水源日入库流量平均值、入库水量
     *
     * @param date 日期
     * @return 入库流量平均值、入库水量
     */
    Pair<Double, Double> getMainDayWaterVolume(LocalDate date);

    /**
     * 获取指定时间的主水源库容量
     *
     * @param date 时间
     * @param frequency 计算频率
     * @return 主水源库容
     */
    double getMainStorageCapacityByHistory(LocalDate date, Integer frequency);

    /**
     * 获取指定时间的主水源可供水量
     *
     * @param date 时间
     * @param frequency 计算频率
     * @return 主水源可供水量
     */
    double getMainWaterSupplyVolumeByHistory(LocalDate date, Integer frequency);

}
