package com.yutoudev.irrigation.irr.service.measind;

import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelResultVO;
import com.yutoudev.irrigation.framework.excel.core.util.DictConvertUtils;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindtime.*;
import com.yutoudev.irrigation.irr.convert.measind.MeasIndTimeConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndTimeDO;
import com.yutoudev.irrigation.irr.dal.mysql.measind.MeasIndTimeMapper;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import io.github.portaldalaran.talons.core.TalonsHelper;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import io.github.portaldalaran.talons.exception.TalonsUniqueException;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.exception.enums.GlobalErrorCodeConstants.UNIQUE_FIELD;
import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.irr.enums.EquipErrorCodeConstants.*;
import static com.yutoudev.irrigation.irr.mq.MqConfigConstants.MEAS_DAY_WATERFALL_HOUR_DIFFERENCE;
import static com.yutoudev.irrigation.irr.mq.MqConfigConstants.MEAS_WATER_LEVEL_HOUR_REPORT_TIMEOUT;

/**
 * 量测设备指标统计实时Service实现类
 *
 * <AUTHOR>
 * @description 管理后台-量测设备指标统计实时Service实现类
 * @time 2024-08-06 16:32:26
 */
@Service
@Validated
@Slf4j
public class MeasIndTimeServiceImpl extends TalonsServiceImpl<MeasIndTimeMapper, MeasIndTimeDO>
        implements MeasIndTimeService<MeasIndTimeDO> {

    @Resource
    private MeasIndTimeMapper measIndTimeMapper;

    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;

    @Resource
    private TalonsHelper talonsHelper;

    @Override
    public void checkField(MeasIndTimeDO entity) {
        try {
            super.checkField(entity);
        } catch (TalonsUniqueException e) {
            throw exception(UNIQUE_FIELD, e.getMessage(), e.getValue());
        }
    }

    @Override
    public Long create(MeasIndTimeCreateReqVO createReqVO) {
        // 插入
        MeasIndTimeDO measIndTime = MeasIndTimeConvert.INSTANCE.convert(createReqVO);
        this.save(measIndTime, true);
        // 返回
        return measIndTime.getId();
    }

    @Override
    public boolean createBatch(List<MeasIndTimeCreateReqVO> list) {

        List<MeasIndTimeDO> saveList = MeasIndTimeConvert.INSTANCE.convertCreateBatch(list);
        if (this.saveBatch(saveList, true)) {
            return true;
        } else {
            throw exception(MEAS_IND_TIME_SAVE_BATCH_ERROR);
        }
    }

    @Override
    public boolean saveBatch(List<MeasIndTimeDO> list) {
        return measIndTimeMapper.insertOrUpdateBatch(list);
    }

    @Override
    public void update(MeasIndTimeUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateMeasIndTimeExists(updateReqVO.getId());
        // 更新
        MeasIndTimeDO updateObj = MeasIndTimeConvert.INSTANCE.convert(updateReqVO);
        this.updateById(updateObj, true);
    }

    @Override
    public boolean updateBatch(List<MeasIndTimeUpdateReqVO> list) {

        List<MeasIndTimeDO> updateList = MeasIndTimeConvert.INSTANCE.convertUpdateBatch(list);

        for (MeasIndTimeDO tempDO : updateList) {
            // 校验存在,因为存进来转化就是UpdateReqVO
            this.validateMeasIndTimeExists(tempDO.getId());
        }

        if (this.updateBatchById(updateList, true)) {
            return true;
        } else {
            throw exception(MEAS_IND_TIME_UPDATE_BATCH_ERROR);
        }
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateMeasIndTimeExists(id);
        // 删除
        this.removeById(id, true);
    }

    @Override
    public boolean deleteBatch(List<Long> ids) {
        if (this.removeByIds(ids, true)) {
            return true;
        } else {
            throw exception(MEAS_IND_TIME_DELETE_BATCH_ERROR);
        }
    }

    private void validateMeasIndTimeExists(Long id) {
        if (measIndTimeMapper.selectById(id) == null) {
            throw exception(MEAS_IND_TIME_NOT_EXISTS);
        }
    }

    @Override
    public MeasIndTimeDO get(Long id) {
        return this.getById(id, true);
    }

    @Override
    public MeasIndTimeDO getEightDataByEquipIdAndTime(String centralId, String devId, LocalDateTime time) {
        LocalDateTime less = time.minusHours(1);
        if (less.getMinute() <= MEAS_WATER_LEVEL_HOUR_REPORT_TIMEOUT) {
            less = less.withMinute(MEAS_WATER_LEVEL_HOUR_REPORT_TIMEOUT);
        }
        return this.getOne(new LambdaQueryWrapperX<MeasIndTimeDO>()
                .eq(MeasIndTimeDO::getCentralId, centralId)
                .eq(MeasIndTimeDO::getDevId, devId)
                .ge(MeasIndTimeDO::getReportTime, less)
                .lt(MeasIndTimeDO::getReportTime, time.withMinute(0).withSecond(0))
                .orderByDesc(MeasIndTimeDO::getReportTime)
                .last("limit 1"));
    }

    @Override
    public MeasIndTimeDO getLastHourValidDataByEquipIdAndTime(String centralId, String devId, LocalDateTime time) {
        LocalDateTime lastClockTime = time.withMinute(0).withSecond(0);
        LocalDateTime formTime = lastClockTime.minusMinutes(MEAS_WATER_LEVEL_HOUR_REPORT_TIMEOUT);
        return this.getOne(new LambdaQueryWrapperX<MeasIndTimeDO>()
                .eq(MeasIndTimeDO::getCentralId, centralId)
                .eq(MeasIndTimeDO::getDevId, devId)
                .gt(MeasIndTimeDO::getReportTime, formTime)
                .le(MeasIndTimeDO::getReportTime, lastClockTime)
                .orderByDesc(MeasIndTimeDO::getReportTime)
                .last("limit 1"));
    }

    @Override
    public MeasIndTimeDO getLastDataByEquipId(String centralId, String devId) {
        return this.getOne(new LambdaQueryWrapperX<MeasIndTimeDO>()
                .eq(MeasIndTimeDO::getCentralId, centralId)
                .eq(MeasIndTimeDO::getDevId, devId)
                .orderByDesc(MeasIndTimeDO::getReportTime)
                .last("limit 1"));
    }

    @Override
    public MeasIndTimeDO getLastDataByEquipIdAndDefineTime(String centralId, String devId, LocalDateTime time) {
        return this.getOne(new LambdaQueryWrapperX<MeasIndTimeDO>()
                .eq(MeasIndTimeDO::getCentralId, centralId)
                .eq(MeasIndTimeDO::getDevId, devId)
                .le(MeasIndTimeDO::getReportTime, time)
                .orderByDesc(MeasIndTimeDO::getReportTime)
                .last("limit 1"));
    }

    @Override
    public MeasIndTimeDO getLastDataByEquipIdAndTime(String centralId, String devId, LocalDateTime time) {

        if (time.isBefore(time.withHour(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).withMinute(1).withSecond(0))) {
            time = time.minusDays(1).withHour(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).withMinute(0).withSecond(0);
        } else {
            time = time.withHour(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).withMinute(0).withSecond(0);
        }

        return this.getOne(new LambdaQueryWrapperX<MeasIndTimeDO>()
                .eq(MeasIndTimeDO::getCentralId, centralId)
                .eq(MeasIndTimeDO::getDevId, devId)
                .gt(MeasIndTimeDO::getReportTime, time)
                .le(MeasIndTimeDO::getReportTime,
                        time.plusDays(1).withHour(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).withMinute(0).withSecond(0))
                .orderByDesc(MeasIndTimeDO::getReportTime)
                .last("limit 1"));
    }

    @Override
    public MeasIndTimeDO getDataByEquipIdAndGivenTime(String centralId, String devId, LocalDateTime time) {
        return this.getOne(new LambdaQueryWrapperX<MeasIndTimeDO>()
                .eq(MeasIndTimeDO::getCentralId, centralId)
                .eq(MeasIndTimeDO::getDevId, devId)
                .eq(MeasIndTimeDO::getReportTime, time));
    }

    @Override
    public List<MeasIndTimeDO> getYesterdayDataByEquipId(String centralId, String devId) {
        return measIndTimeMapper.selectList(new LambdaQueryWrapperX<MeasIndTimeDO>()
                .eq(MeasIndTimeDO::getCentralId, centralId)
                .eq(MeasIndTimeDO::getDevId, devId)
                .gt(MeasIndTimeDO::getReportTime,
                        LocalDateTime.now().minusDays(1).withHour(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).withMinute(0)
                                .withSecond(0).withNano(0))
                .le(MeasIndTimeDO::getReportTime,
                        LocalDateTime.now().withHour(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).withMinute(0).withSecond(0)
                                .withNano(0))
                .orderByDesc(MeasIndTimeDO::getReportTime));
    }

    @Override
    public List<MeasIndTimeDO> getLastHourDataByEquipIdAndTime(String centralId, String devId, LocalDateTime time) {
        return measIndTimeMapper.selectList(new LambdaQueryWrapperX<MeasIndTimeDO>()
                .eq(MeasIndTimeDO::getCentralId, centralId)
                .eq(MeasIndTimeDO::getDevId, devId)
                .gt(MeasIndTimeDO::getReportTime, time.minusHours(1).withMinute(0).withSecond(0).withNano(0))
                .le(MeasIndTimeDO::getReportTime, time.withMinute(0).withSecond(0).withNano(0))
                .orderByDesc(MeasIndTimeDO::getReportTime));
    }

    @Override
    public List<MeasIndTimeDO> getList(List<Long> ids) {
        return this.selectBatchIds(ids, true);
    }

    @Override
    public List<MeasIndTimeDO> getListByEquipIdAndDayTime(String centralId, String devId, LocalDateTime time) {
        return measIndTimeMapper.selectList(new LambdaQueryWrapperX<MeasIndTimeDO>()
                .eq(MeasIndTimeDO::getCentralId, centralId)
                .eq(MeasIndTimeDO::getDevId, devId)
                .gt(MeasIndTimeDO::getReportTime, time.withHour(8).withMinute(0).withSecond(0))
                .le(MeasIndTimeDO::getReportTime, time.plusDays(1).withHour(8).withMinute(0).withSecond(0))
                .orderByDesc(MeasIndTimeDO::getReportTime));
    }

    @Override
    public List<MeasIndTimeDO> getListByEquipIdAndMonthTime(String centralId, String devId, LocalDateTime time) {
        return measIndTimeMapper.selectList(new LambdaQueryWrapperX<MeasIndTimeDO>()
                .eq(MeasIndTimeDO::getCentralId, centralId)
                .eq(MeasIndTimeDO::getDevId, devId)
                .gt(MeasIndTimeDO::getReportTime, time.withHour(8).withMinute(0).withSecond(0))
                .le(MeasIndTimeDO::getReportTime, time.plusMonths(1).withHour(8).withMinute(0).withSecond(0))
                .orderByDesc(MeasIndTimeDO::getReportTime));
    }

    @Override
    public PageResult<MeasIndTimeDO> page(MeasIndTimePageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<MeasIndTimeDO> queryBuilder = new QueryCriteriaWrapperBuilder<MeasIndTimeDO>() {
        };
        queryBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<MeasIndTimeDO> pageResult = measIndTimeMapper.selectPage(pageParam, queryBuilder.getQueryWrapper());
        talonsHelper.query(pageResult.getList(), this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return pageResult;
    }

    @Override
    public List<MeasIndTimeDO> getList(MeasIndTimeQueryReqVO queryReqVO) {
        QueryCriteriaWrapperBuilder<MeasIndTimeDO> queryBuilder = new QueryCriteriaWrapperBuilder<MeasIndTimeDO>() {
        };
        queryBuilder.build(queryReqVO);

        List<MeasIndTimeDO> result = measIndTimeMapper.selectList(queryBuilder.getQueryWrapper());
        talonsHelper.query(result, this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return result;
    }

    @Override
    public ImportExcelRespVO importExcel(List<MeasIndTimeExcelVO> importList, boolean isUpdateSupport) {
        if (importList == null || importList.isEmpty()) {
            throw exception(MEAS_IND_TIME_IMPORT_LIST_IS_EMPTY);
        }

        // todo 如果有数据权限
        ImportExcelRespVO importExcelRespVO = ImportExcelRespVO.builder()
                .insertSuccess(new ArrayList<>())
                .updateSuccess(new ArrayList<>())
                .failures(new ArrayList<>()).build();

        List<MeasIndTimeDO> saveList = MeasIndTimeConvert.INSTANCE.convertImportExcel(importList);

        for (int i = 0; i < saveList.size(); i++) {
            MeasIndTimeDO po = saveList.get(i);
            boolean isSave = Objects.isNull(po.getId());
            boolean isSuccess = false;
            ImportExcelResultVO fail = new ImportExcelResultVO();

            try {
                // todo 如果有关联对象
                DictConvertUtils.fill(po, MeasIndTimeExcelVO.class);
                this.checkField(po);
                isSuccess = saveOrUpdate(po);
            } catch (Exception e) {
                fail.setIndex(i + 1);
                fail.setValue(e.getMessage());
            }

            if (isSuccess) {
                ExcelUtils.successResult(importExcelRespVO, new ImportExcelResultVO(i + 1, ""), isSave);
            } else {
                if (fail.getIndex() == 0) {
                    fail.setIndex(i + 1);
                    fail.setValue("未知");
                }
                importExcelRespVO.getFailures().add(fail);
            }
        }
        return importExcelRespVO;
    }
}
