package com.yutoudev.irrigation.irr.service.measind;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelResultVO;
import com.yutoudev.irrigation.framework.excel.core.util.DictConvertUtils;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindday.*;
import com.yutoudev.irrigation.irr.convert.measind.MeasIndDayConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndDayDO;
import com.yutoudev.irrigation.irr.dal.mysql.measind.MeasIndDayMapper;
import io.github.portaldalaran.talons.core.TalonsHelper;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import io.github.portaldalaran.talons.exception.TalonsUniqueException;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.exception.enums.GlobalErrorCodeConstants.UNIQUE_FIELD;
import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.irr.enums.EquipErrorCodeConstants.*;

/**
 * 量测设备指标统计日Service实现类
 *
 * <AUTHOR>
 * @description 管理后台-量测设备指标统计日Service实现类
 * @time 2024-06-16 20:28:51
 */
@Service
@Validated
public class MeasIndDayServiceImpl extends TalonsServiceImpl<MeasIndDayMapper, MeasIndDayDO> implements MeasIndDayService<MeasIndDayDO> {

    @Resource
    private MeasIndDayMapper measIndDayMapper;

    @Resource
    private TalonsHelper talonsHelper;

    @Override
    public void checkField(MeasIndDayDO entity) {
        try {
            super.checkField(entity);
        } catch (TalonsUniqueException e) {
            throw exception(UNIQUE_FIELD, e.getMessage(), e.getValue());
        }
    }

    @Override
    public Long create(MeasIndDayCreateReqVO createReqVO) {
        // 插入
        MeasIndDayDO measIndDay = MeasIndDayConvert.INSTANCE.convert(createReqVO);
        this.save(measIndDay, true);
        // 返回
        return measIndDay.getId();
    }

    @Override
    public boolean createBatch(List<MeasIndDayCreateReqVO> list) {

        List<MeasIndDayDO> saveList = MeasIndDayConvert.INSTANCE.convertCreateBatch(list);
        if (this.saveBatch(saveList, true)) {
            return true;
        } else {
            throw exception(MEAS_IND_DAY_SAVE_BATCH_ERROR);
        }
    }

    @Override
    public boolean saveBatch(List<MeasIndDayDO> list) {
        return measIndDayMapper.insertOrUpdateBatch(list);
    }

    @Override
    public void update(MeasIndDayUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateMeasIndDayExists(updateReqVO.getId());
        // 更新
        MeasIndDayDO updateObj = MeasIndDayConvert.INSTANCE.convert(updateReqVO);
        this.updateById(updateObj, true);
    }


    @Override
    public boolean updateBatch(List<MeasIndDayUpdateReqVO> list) {

        List<MeasIndDayDO> updateList = MeasIndDayConvert.INSTANCE.convertUpdateBatch(list);

        for (MeasIndDayDO tempDO : updateList) {
            // 校验存在,因为存进来转化就是UpdateReqVO
            this.validateMeasIndDayExists(tempDO.getId());
        }

        if (this.updateBatchById(updateList, true)) {
            return true;
        } else {
            throw exception(MEAS_IND_DAY_UPDATE_BATCH_ERROR);
        }
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateMeasIndDayExists(id);
        // 删除
        this.removeById(id, true);
    }

    @Override
    public boolean deleteBatch(List<Long> ids) {
        if (this.removeByIds(ids, true)) {
            return true;
        } else {
            throw exception(MEAS_IND_DAY_DELETE_BATCH_ERROR);
        }
    }

    private void validateMeasIndDayExists(Long id) {
        if (measIndDayMapper.selectById(id) == null) {
            throw exception(MEAS_IND_DAY_NOT_EXISTS);
        }
    }

    @Override
    public MeasIndDayDO get(Long id) {
        return this.getById(id, true);
    }

    @Override
    public MeasIndDayDO getByEquipAndDay(String centralId, String devId, String day) {
        return this.getOne(new LambdaQueryWrapperX<MeasIndDayDO>()
                .eq(MeasIndDayDO::getCentralId, centralId)
                .eq(MeasIndDayDO::getDevId, devId)
                .eq(MeasIndDayDO::getDay, day));
    }

    @Override
    public List<MeasIndDayDO> getList(List<Long> ids) {
        return this.selectBatchIds(ids, true);
    }

    @Override
    public List<MeasIndDayDO> getListByEquipIdsAndDay(List<Long> ids, String day) {
        return this.list(new LambdaQueryWrapperX<MeasIndDayDO>()
                .in(MeasIndDayDO::getEquipId, ids)
                .eq(MeasIndDayDO::getDay, day)
        );
    }

    @Override
    public List<MeasIndDayDO> getListByEquipIdsAndDayRange(List<Long> ids, String yearMonth) {
        return this.list(new QueryWrapper<MeasIndDayDO>()
                .in("equip_id", ids)
                .apply("date_format(day,'%Y-%m') = {0}", yearMonth)
        );
    }

    @Override
    public List<MeasIndDayDO> getListByEquipAndDayRange(EquipBaseDO equip, String yearMonth) {
        return this.list(new QueryWrapper<MeasIndDayDO>()
                .eq("equip_id", equip.getId())
                .apply("date_format(day,'%Y-%m') = {0}", yearMonth)
        );
    }

    @Override
    public PageResult<MeasIndDayDO> page(MeasIndDayPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<MeasIndDayDO> queryBuilder = new QueryCriteriaWrapperBuilder<MeasIndDayDO>() {
        };
        queryBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<MeasIndDayDO> pageResult = measIndDayMapper.selectPage(pageParam, queryBuilder.getQueryWrapper());
        talonsHelper.query(pageResult.getList(), this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return pageResult;
    }

    @Override
    public List<MeasIndDayDO> getList(MeasIndDayQueryReqVO queryReqVO) {
        QueryCriteriaWrapperBuilder<MeasIndDayDO> queryBuilder = new QueryCriteriaWrapperBuilder<MeasIndDayDO>() {
        };
        queryBuilder.build(queryReqVO);

        List<MeasIndDayDO> result = measIndDayMapper.selectList(queryBuilder.getQueryWrapper());
        talonsHelper.query(result, this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return result;
    }

    @Override
    public ImportExcelRespVO importExcel(List<MeasIndDayExcelVO> importList, boolean isUpdateSupport) {
        if (importList == null || importList.isEmpty()) {
            throw exception(MEAS_IND_DAY_IMPORT_LIST_IS_EMPTY);
        }

        //todo 如果有数据权限
        ImportExcelRespVO importExcelRespVO = ImportExcelRespVO.builder()
                .insertSuccess(new ArrayList<>())
                .updateSuccess(new ArrayList<>())
                .failures(new ArrayList<>()).build();

        List<MeasIndDayDO> saveList = MeasIndDayConvert.INSTANCE.convertImportExcel(importList);

        for (int i = 0; i < saveList.size(); i++) {
            MeasIndDayDO po = saveList.get(i);
            boolean isSave = Objects.isNull(po.getId());
            boolean isSuccess = false;
            ImportExcelResultVO fail = new ImportExcelResultVO();

            try {
                //todo 如果有关联对象
                DictConvertUtils.fill(po, MeasIndDayExcelVO.class);
                this.checkField(po);
                isSuccess = saveOrUpdate(po);
            } catch (Exception e) {
                fail.setIndex(i + 1);
                fail.setValue(e.getMessage());
            }

            if (isSuccess) {
                ExcelUtils.successResult(importExcelRespVO, new ImportExcelResultVO(i + 1, ""), isSave);
            } else {
                if (fail.getIndex() == 0) {
                    fail.setIndex(i + 1);
                    fail.setValue("未知");
                }
                importExcelRespVO.getFailures().add(fail);
            }
        }
        return importExcelRespVO;
    }
}
