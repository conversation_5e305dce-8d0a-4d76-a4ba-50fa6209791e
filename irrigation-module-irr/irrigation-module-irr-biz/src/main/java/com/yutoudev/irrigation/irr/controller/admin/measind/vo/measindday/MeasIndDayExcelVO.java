package com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindday;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 *
 * 量测设备指标统计日ExcelVO
 * @description 管理后台-量测设备指标统计日导出、导入ExcelVO
 * <AUTHOR>
 * @time 2024-06-16 20:28:51
 *
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class MeasIndDayExcelVO {


    /**
     * ID
     * 
     */
    @ExcelProperty("ID")
    private Long id;


    /**
     * 站点ID
     *
     */
    @ExcelProperty("站点ID")
    private Long equipId;

    /**
     * 中心站ID
     * 
     */
    @ExcelProperty("中心站ID")
    private String centralId;

    /**
     * 遥测站ID
     * 
     */
    @ExcelProperty("遥测站ID")
    private String devId;

    /**
     * 日期
     * 
     */
    @ExcelProperty("日期")
    private String day;

    /**
     * 8点水位
     *
     */
    @ExcelProperty("8点水位")
    private Float eightWaterLevel;

    /**
     * 20点水位
     *
     */
    @ExcelProperty("20点水位")
    private Float twentyWaterLevel;

    /**
     * 日最高水位
     *
     */
    @ExcelProperty("日最高水位")
    private Float maxWaterLevel;

    /**
     * 日最低水位
     *
     */
    @ExcelProperty("日最低水位")
    private Float minWaterLevel;

    /**
     * 日平均水位
     *
     */
    @ExcelProperty("日平均水位")
    private Float avgWaterLevel;

    /**
     * 日最大流量
     *
     */
    @ExcelProperty("日最大流量")
    private Float maxWaterFlow;

    /**
     * 日最小流量
     *
     */
    @ExcelProperty("日最小流量")
    private Float minWaterFlow;

    /**
     * 日平均流量
     *
     */
    @ExcelProperty("日平均流量")
    private Float avgWaterFlow;

    /**
     * 日入库水量
     *
     */
    @ExcelProperty("日入库水量")
    private Float waterVolume;

    /**
     * 日供水量
     *
     */
    @ExcelProperty("日供水量")
    private Float waterSupply;

    /**
     * 日降水量累计值
     *
     */
    @ExcelProperty("日降水量累计值")
    private Float rainfallVolume;

    /**
     * 更新时间
     *
     */
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;
}
