package com.yutoudev.irrigation.irr.service.waterdatacalc;

import com.yutoudev.irrigation.framework.common.util.object.BeanUtils;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefLevelCapacityDO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndHourDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndTimeDO;
import com.yutoudev.irrigation.irr.dal.dataobject.swhsbase.SwhsBaseDO;
import com.yutoudev.irrigation.irr.enums.EquipDataCalcFrequencyEnum;
import com.yutoudev.irrigation.irr.service.coef.CoefLevelCapacityService;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import com.yutoudev.irrigation.irr.service.iot.tools.CalcDateTimeHelper;
import com.yutoudev.irrigation.irr.service.measind.MeasIndHourService;
import com.yutoudev.irrigation.irr.service.measind.MeasIndTimeService;
import com.yutoudev.irrigation.irr.service.swhsbase.SwhsBaseService;
import com.yutoudev.irrigation.module.infra.api.config.ConfigApi;
import com.yutoudev.irrigation.module.infra.api.config.dto.ApiConfigRespDTO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.util.number.PrecisionUtils.convert;
import static com.yutoudev.irrigation.irr.enums.IrrigationGlobalConstants.*;
import static com.yutoudev.irrigation.irr.mq.MqConfigConstants.EQUIP_AUTO_REPORT_PERIOD;

/**
 * <AUTHOR>
 */
@Service
public class ReservoirDataCalcServiceImpl extends BaseDataCalcService implements ReservoirDataCalcService {

    @Resource
    private SwhsBaseService<SwhsBaseDO> swhsBaseService;

    @Resource
    private MeasIndTimeService<MeasIndTimeDO> measIndTimeService;

    @Resource
    private MeasIndHourService<MeasIndHourDO> measIndHourService;

    @Resource
    private CoefLevelCapacityService<CoefLevelCapacityDO> coefLevelCapacityService;

    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;

    @Resource
    private ConfigApi configApi;

    public EquipBaseCacheVO getMainWaterSourceEquip() {
        // 获取主水源计算设备
        ApiConfigRespDTO config = configApi.getConfigByKey(WATER_LEVEL_RESERVOIR);
        String[] split = config.getValue().split(",");
        return equipBaseService.getCacheByCentralIdAndDevId(split[0], split[1]);
    }

    public String[] getMainWaterSourceOutFlowEquipId() {
        // 获取主水源出库流量计算设备
        ApiConfigRespDTO config = configApi.getConfigByKey(MAIN_RESERVOIR_OUT_FLOW_DEVICES);
        return config.getValue().split(";");
    }

    @Override
    public double getMainWaterCapacityByWaterLevel(EquipBaseCacheVO equip, double waterLevel) {
        double storageCapacity = coefLevelCapacityService.getStorageCapacity(equip.getId(), convert(waterLevel, 3));
        return convert(storageCapacity, 4);
    }

    @Override
    public double getMainWaterCapacityDiff(EquipBaseCacheVO equip, double waterLevel) {
        MeasIndTimeDO lastTimeData = measIndTimeService.getLastDataByEquipId(equip.getCentralId(),
                equip.getDevId());
        // 如果没有数据或数据在1小时以前不参与计算
        if (Objects.isNull(lastTimeData) || LocalDateTime.now().minusHours(1).isAfter(lastTimeData.getReportTime())) {
            return 0;
        } else {
            double currentCapacity = getMainWaterCapacityByWaterLevel(equip, waterLevel);
            return convert(currentCapacity - lastTimeData.getWaterVolume(), 4);
        }
    }

    @Override
    public Pair<Double, Double> getOutFlowAndVolume(LocalDateTime time) {
        String[] equipList = getMainWaterSourceOutFlowEquipId();
        double outFlow = 0.0;
        double outWaterVolume = 0.0;
        for (String equipInfo : equipList) {
            String[] equipId = equipInfo.split(",");
            EquipBaseCacheVO calcEquip = equipBaseService.getCacheByCentralIdAndDevId(equipId[0], equipId[1]);
            if (Objects.isNull(calcEquip)) {
                continue;
            }
            MeasIndTimeDO measIndTime = measIndTimeService.getLastDataByEquipIdAndDefineTime(calcEquip.getCentralId(), calcEquip.getDevId(), time);
            if (Objects.nonNull(measIndTime) && ChronoUnit.MINUTES.between(measIndTime.getReportTime(), time) <= 5) {
                if (Objects.nonNull(measIndTime.getCalcWaterFlow())) {
                    outFlow += measIndTime.getCalcWaterFlow();
                    outWaterVolume += measIndTime.getCalcWaterVolume();
                }
            }
        }
        return Pair.of(outFlow, convert(outWaterVolume, 2));
    }

    @Override
    public Triple<Double, Double, Double> getInFlowAndVolume(LocalDateTime time) {
        EquipBaseCacheVO equip = getMainWaterSourceEquip();
        List<MeasIndTimeDO> timeDataList = measIndTimeService.getLastHourDataByEquipIdAndTime(equip.getCentralId(), equip.getDevId(), time);
        if (timeDataList.size() > 1) {
            if (Objects.isNull(timeDataList.get(0).getReservoirOutVolume())
                    || Objects.isNull(timeDataList.get(timeDataList.size() - 1).getReservoirOutVolume())) {
                return Triple.of(0.0, 0.0, 0.0);
            }

            // 库容差，这里库容差为小时库容差
            double capacityDiff;
            MeasIndHourDO lastHourData = measIndHourService.getByEquipAndHour(equip.getCentralId(), equip.getDevId(), time.minusHours(1));
            if (Objects.isNull(lastHourData) || lastHourData.getWaterLevel() == 0) {
                capacityDiff = 0;
            } else {
                capacityDiff = timeDataList.get(0).getWaterVolume() - getMainWaterCapacityByWaterLevel(equip, lastHourData.getWaterLevel());
            }

            if (capacityDiff < 0) {
                return Triple.of(0.0, 0.0, capacityDiff);
            }
            // 出库水量
            double outWaterVolume = timeDataList.stream().mapToDouble(MeasIndTimeDO::getReservoirOutVolume).sum();
            // 时间差
            long seconds = ChronoUnit.SECONDS.between(timeDataList.get(timeDataList.size() - 1).getReportTime(),
                    timeDataList.get(0).getReportTime()) + EQUIP_AUTO_REPORT_PERIOD;
            BigDecimal outWaterVolumeBd = new BigDecimal(capacityDiff).multiply(new BigDecimal(100000000)).add(new BigDecimal(outWaterVolume));

            // 入库水量/来水
            double inWaterVolume = outWaterVolumeBd.divide(new BigDecimal(10000), 3, RoundingMode.HALF_UP).doubleValue();

            // 入库流量
            double inflow = outWaterVolumeBd.divide(new BigDecimal(seconds), 3, RoundingMode.HALF_UP).doubleValue();

            return Triple.of(inflow, inWaterVolume, convert(capacityDiff, 4));
        }
        return Triple.of(0.0, 0.0, 0.0);
    }

    @Override
    public double getMainWaterSupplyVolumeByWaterLevel(double waterLevel) {
        EquipBaseCacheVO equip = getMainWaterSourceEquip();
        // 库容
        double storageCapacity = coefLevelCapacityService.getStorageCapacity(equip.getId(), waterLevel);

        // 获取主水源
        SwhsBaseDO mainWaterSource = swhsBaseService.get(swhsBaseService.getMainReservoirId());

        if (Objects.isNull(mainWaterSource.getDeadWaterLevel())) {
            return 0d;
        }

        // 计算死库容
        double deadStorageCapacity = coefLevelCapacityService.getStorageCapacity(equip.getId(), mainWaterSource.getDeadWaterLevel());

        return BigDecimal.valueOf(storageCapacity - deadStorageCapacity).multiply(new BigDecimal(10000))
                .setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    @Override
    public double getMainWaterSupplyVolumeByStorageCapacity(double storageCapacity) {
        // 获取主水源
        SwhsBaseDO mainWaterSource = swhsBaseService.get(swhsBaseService.getMainReservoirId());

        if (Objects.isNull(mainWaterSource.getDeadWaterLevel())) {
            return 0d;
        }

        // 获取主水源计算设备
        EquipBaseCacheVO equip = getMainWaterSourceEquip();

        // 计算死库容
        double deadStorageCapacity = coefLevelCapacityService.getStorageCapacity(equip.getId(), mainWaterSource.getDeadWaterLevel());

        return BigDecimal.valueOf(storageCapacity - deadStorageCapacity).multiply(new BigDecimal(10000))
                .setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    @Override
    public double getMainWaterSupplyVolumeByToday() {
        // 获取主水源计算设备
        EquipBaseCacheVO equip = getMainWaterSourceEquip();
        LocalDateTime hourTime = LocalDateTime.now().withHour(8).withMinute(0).withSecond(0).withNano(0);
        MeasIndHourDO data = measIndHourService.getCurrentDataByEquipAndTime(equip.getCentralId(), equip.getDevId(), hourTime);

        if (Objects.nonNull(data)) {
            double waterLevel = BigDecimal.valueOf(data.getWaterLevel().doubleValue()).setScale(3, RoundingMode.HALF_UP).doubleValue();
            return getMainWaterSupplyVolumeByWaterLevel(waterLevel);
        } else {
            ApiConfigRespDTO configByKey = configApi.getConfigByKey(MAIN_RESERVOIR_OBSERVED_WATER_LEVEL);
            if (Objects.nonNull(configByKey)) {
                return getMainWaterSupplyVolumeByWaterLevel(Double.parseDouble(configByKey.getValue()));
            }
        }
        return 0;
    }

    @Override
    public Pair<Double, Double> getMainDayWaterVolume(LocalDateTime time) {
        EquipBaseCacheVO equip = getMainWaterSourceEquip();
        Pair<LocalDateTime, LocalDateTime> range = CalcDateTimeHelper.getCalcHourRange(time);
        List<MeasIndHourDO> hourList = measIndHourService.getListByEquipAndDateRange(BeanUtils.toBean(equip,
                EquipBaseDO.class), range.getLeft(), range.getRight());
        

        return null;
    }

    @Override
    public double getMainStorageCapacityByHistory(LocalDate date, Integer frequency) {
        EquipBaseCacheVO equip = getMainWaterSourceEquip();
        LocalDateTime hourTime = LocalDateTime.of(date, LocalTime.of(8, 0, 0));
        // 计算库容
        if (Objects.equals(frequency, EquipDataCalcFrequencyEnum.MONTH.getValue())) {
            hourTime = hourTime.withDayOfMonth(1);
        } else if (Objects.equals(frequency, EquipDataCalcFrequencyEnum.YEAR.getValue())) {
            hourTime = hourTime.withMonth(1).withDayOfMonth(1);
        }

        MeasIndHourDO data = measIndHourService.getCurrentDataByEquipAndTime(equip.getCentralId(), equip.getDevId(), hourTime);

        if (Objects.isNull(data)) {
            return 0;
        }

        return coefLevelCapacityService.getStorageCapacity(equip.getId(), BigDecimal.valueOf(data.getWaterLevel())
                .setScale(3, RoundingMode.HALF_UP).doubleValue());
    }

    @Override
    public double getMainWaterSupplyVolumeByHistory(LocalDate date, Integer frequency) {
        double storageCapacity = getMainStorageCapacityByHistory(date, frequency);
        return getMainWaterSupplyVolumeByStorageCapacity(storageCapacity);
    }
}
