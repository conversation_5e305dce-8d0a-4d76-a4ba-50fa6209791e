package com.yutoudev.irrigation.irr.service.iot.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindday.MeasIndDayCreateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindday.MeasIndDayUpdateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourCreateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourUpdateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindminute.MeasIndMinuteCreateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindmonth.MeasIndMonthCreateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindmonth.MeasIndMonthUpdateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindtime.MeasIndTimeCreateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindyear.MeasIndYearCreateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindyear.MeasIndYearUpdateReqVO;
import com.yutoudev.irrigation.irr.convert.measind.MeasIndTimeConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.*;
import com.yutoudev.irrigation.irr.enums.MeasCleanTableTypeEnum;
import com.yutoudev.irrigation.irr.mq.message.meas.MeasReportReceiveQueueMessage;
import com.yutoudev.irrigation.irr.service.iot.strategy.AbstractDeviceDataStrategy;
import com.yutoudev.irrigation.irr.service.iot.strategy.OperationStrategyEnum;
import com.yutoudev.irrigation.irr.service.measind.*;
import com.yutoudev.irrigation.irr.service.waterdatacalc.BaseDataCalcService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.function.TriFunction;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.lang.NonNull;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.DoubleSummaryStatistics;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.yutoudev.irrigation.framework.common.util.number.PrecisionUtils.convert;
import static com.yutoudev.irrigation.irr.mq.MqConfigConstants.*;

/**
 * 基础数据清洗策略
 * 使用DataCleaningService进行数据清洗和入库
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseDataCleaningStrategy extends AbstractDeviceDataStrategy {

    @Resource
    private MeasIndTimeService<MeasIndTimeDO> measIndTimeService;

    @Resource
    private MeasIndMinuteService<MeasIndMinuteDO> measIndMinuteService;

    @Resource
    private MeasIndHourService<MeasIndHourDO> measIndhourService;

    @Resource
    private MeasIndDayService<MeasIndDayDO> measIndDayService;

    @Resource
    private MeasIndMonthService<MeasIndMonthDO> measIndMonthService;

    @Resource
    private MeasIndYearService<MeasIndYearDO> measIndYearService;

    @Resource
    private BaseDataCalcService baseDataCalcService;

    @Override
    public void processTimeData(EquipBaseCacheVO equip, List<MeasReportReceiveQueueMessage> list, LocalDateTime updateTime) {
        try {
            // 过滤需要清洗的数据
            List<MeasReportReceiveQueueMessage> filteredList = filterCleanData(MeasCleanTableTypeEnum.TIME.getType(), equip, list);
            if (isEmpty(filteredList)) {
                return;
            }

            // 获取第一条消息作为模板
            MeasReportReceiveQueueMessage messageTemplate = filteredList.get(0);

            // 创建实时数据对象
            MeasIndTimeCreateReqVO create = new MeasIndTimeCreateReqVO();
            LocalDateTime reportTime = LocalDateTime.parse(messageTemplate.getDateTime(),
                    DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
            create.setEquipId(equip.getId());
            create.setCentralId(messageTemplate.getCentralId());
            create.setDevId(messageTemplate.getDevId());
            create.setReportTime(reportTime);

            // 处理各个指标
            for (MeasReportReceiveQueueMessage msg : filteredList) {
                if (Objects.equals(msg.getVariableType(), MEAS_INDICATES_TYPE_RIVER_WATER_LEVEL)
                        || Objects.equals(msg.getVariableType(), MEAS_INDICATES_TYPE_RESERVOIR_WATER_LEVEL)) {
                    // 处理水位指标
                    create.setWaterLevel(msg.getVariableValue());
                } else if (Objects.equals(msg.getVariableType(), MEAS_INDICATES_TYPE_INSTANTANEOUS_SPEED)) {
                    // 处理流速指标
                    create.setWaterSpeed(msg.getVariableValue());
                } else if (Objects.equals(msg.getVariableType(), MEAS_INDICATES_TYPE_INSTANTANEOUS_FLOW)) {
                    // 处理流量指标
                    create.setWaterFlow(msg.getVariableValue());
                    create.setCalcWaterFlow(msg.getVariableValue());
                } else if (Objects.equals(msg.getVariableType(), MEAS_INDICATES_TYPE_ACCUMULATED_WATER_VOLUME)) {
                    // 处理累计水量指标
                    create.setWaterVolume(msg.getVariableValue());
                } else if (Objects.equals(msg.getVariableType(), MEAS_INDICATES_TYPE_VOLTAGE)) {
                    // 处理电压指标
                    create.setVoltage(msg.getVariableValue());
                }
            }

            timeCalcConsumer.accept(equip, create);
            create.setUpdateTime(updateTime);

            // 转换为DO并保存
            MeasIndTimeDO measIndTime = MeasIndTimeConvert.INSTANCE.convert(create);
            measIndTimeService.save(measIndTime, true);

        } catch (Exception e) {
            log.error("[processTimeData][处理设备实时数据异常] 设备ID: {}, 类型: {}, 异常: {}",
                    equip.getId(), equip.getDevType(), e.getMessage(), e);
        }
    }

    @Override
    public void processMinuteData(EquipBaseCacheVO equip, List<MeasReportReceiveQueueMessage> list, LocalDateTime updateTime) {
        try {
            // 过滤需要清洗的数据
            List<MeasReportReceiveQueueMessage> filteredList = filterCleanData(MeasCleanTableTypeEnum.MINUTE.getType(), equip, list);
            if (isEmpty(filteredList)) {
                return;
            }

            // 获取第一条消息作为模板
            MeasReportReceiveQueueMessage msg = filteredList.get(0);

            // 创建分钟数据对象
            MeasIndMinuteCreateReqVO create = new MeasIndMinuteCreateReqVO();
            create.setEquipId(equip.getId());
            create.setCentralId(msg.getCentralId());
            create.setDevId(msg.getDevId());

            // 设置上报时间
            LocalDateTime reportTime = LocalDateTime.parse(msg.getDateTime(),
                    DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
            create.setReportTime(reportTime);

            // 处理各个指标
            for (MeasReportReceiveQueueMessage message : filteredList) {
                // 处理降雨量指标
                if (Objects.equals(message.getVariableType(), MEAS_INDICATES_TYPE_RAIN_FIVE_MINUTE)) {
                    create.setRainfall(message.getVariableValue());
                }
                // 处理电压指标
                if (Objects.equals(message.getVariableType(), MEAS_INDICATES_TYPE_VOLTAGE)) {
                    create.setVoltage(message.getVariableValue());
                }
            }

            // 设置更新时间
            create.setUpdateTime(updateTime);

            // 创建记录
            measIndMinuteService.create(create);

        } catch (Exception e) {
            log.error("[processMinuteData][处理设备分钟数据异常] 设备ID: {}, 类型: {}, 异常: {}",
                    equip.getId(), equip.getDevType(), e.getMessage(), e);
        }
    }

    @Override
    public void processHourData(EquipBaseCacheVO equip, List<MeasReportReceiveQueueMessage> list, LocalDateTime updateTime) {
        try {
            // 过滤需要清洗的数据
            List<MeasReportReceiveQueueMessage> filteredList = filterCleanData(MeasCleanTableTypeEnum.HOUR.getType(), equip, list);
            if (isEmpty(filteredList)) {
                return;
            }

            MeasReportReceiveQueueMessage msg = list.get(0);
            LocalDateTime dateTime = LocalDateTime.parse(msg.getDateTime(),
                    DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
            MeasIndHourUpdateReqVO update = new MeasIndHourUpdateReqVO();

            // 设置固定数据
            update.setEquipId(equip.getId());
            update.setCentralId(msg.getCentralId());
            update.setDevId(msg.getDevId());
            update.setHourTime(dateTime);

            // 设置雨量
            filteredList.stream()
                    .filter(message -> {
                        Integer type = message.getVariableType();
                        return Objects.equals(type, MEAS_INDICATES_TYPE_RAIN_FIVE_MINUTE)
                                || Objects.equals(type, MEAS_INDICATES_TYPE_RAIN_HOUR);
                    })
                    .findFirst()
                    .ifPresent(message -> update.setRainfallVolume(message.getVariableValue()));

            // 设置水位
            filteredList.stream()
                    .filter(message -> {
                        Integer type = message.getVariableType();
                        return Objects.equals(type, MEAS_INDICATES_TYPE_RIVER_WATER_LEVEL)
                                || Objects.equals(type, MEAS_INDICATES_TYPE_RESERVOIR_WATER_LEVEL);
                    })
                    .findFirst()
                    .ifPresent(message -> update.setWaterLevel(message.getVariableValue()));

            // 设置电压
            filteredList.stream()
                    .filter(message -> {
                        Integer type = message.getVariableType();
                        return Objects.equals(type, MEAS_INDICATES_TYPE_VOLTAGE);
                    })
                    .findFirst()
                    .ifPresent(message -> update.setVoltage(message.getVariableValue()));

            Integer operation = hourCalcFunction.apply(equip, update);
            update.setUpdateTime(updateTime);

            if (Objects.equals(operation, OperationStrategyEnum.CREATE.getType())) {
                measIndhourService.create(BeanUtil.copyProperties(update, MeasIndHourCreateReqVO.class));
            } else if (Objects.equals(operation, OperationStrategyEnum.UPDATE.getType())) {
                measIndhourService.update(update);
            }

        } catch (Exception e) {
            log.error("[processHourData][处理设备小时数据异常] 设备ID: {}, 类型: {}, 异常: {}",
                    equip.getId(), equip.getDevType(), e.getMessage(), e);
        }
    }

    @Override
    public void processDayData(EquipBaseCacheVO equip, List<MeasReportReceiveQueueMessage> list, LocalDateTime updateTime) {
        try {
            // 过滤需要清洗的数据
            List<MeasReportReceiveQueueMessage> filteredList = filterCleanData(MeasCleanTableTypeEnum.DAY.getType(), equip, list);
            if (isEmpty(filteredList)) {
                return;
            }

            MeasReportReceiveQueueMessage msg = list.get(0);
            LocalDateTime dateTime = LocalDateTime.parse(msg.getDateTime(),
                    DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
            // 日期规则，00:08:01至第二日01:08:00为一天，所以减8小时1分钟
            LocalDateTime realtime = dateTime.minusHours(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).minusMinutes(1).withHour(0)
                    .withMinute(0);
            String convertTime = realtime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));

            MeasIndDayUpdateReqVO update = new MeasIndDayUpdateReqVO();
            update.setEquipId(equip.getId());
            update.setCentralId(msg.getCentralId());
            update.setDevId(msg.getDevId());
            update.setDay(convertTime.substring(0, 10));

            // 设置雨量
            filteredList.stream()
                    .filter(message -> {
                        Integer type = message.getVariableType();
                        return Objects.equals(type, MEAS_INDICATES_TYPE_RAIN_FIVE_MINUTE)
                                || Objects.equals(type, MEAS_INDICATES_TYPE_RAIN_HOUR);
                    })
                    .findFirst()
                    .ifPresent(message -> update.setRainfallVolume(message.getVariableValue()));

            // 设置水位
            filteredList.stream()
                    .filter(message -> {
                        Integer type = message.getVariableType();
                        return Objects.equals(type, MEAS_INDICATES_TYPE_RIVER_WATER_LEVEL)
                                || Objects.equals(type, MEAS_INDICATES_TYPE_RESERVOIR_WATER_LEVEL);
                    })
                    .findFirst()
                    .ifPresent(message -> update.setMinWaterLevel(message.getVariableValue()));

            // 设置流量
            filteredList.stream()
                    .filter(message -> {
                        Integer type = message.getVariableType();
                        return Objects.equals(type, MEAS_INDICATES_TYPE_INSTANTANEOUS_FLOW);
                    })
                    .findFirst()
                    .ifPresent(message -> update.setMinWaterFlow(message.getVariableValue()));

            MeasIndDayDO day = measIndDayService.getByEquipAndDay(msg.getCentralId(),
                    msg.getDevId(), convertTime.substring(0, 10));

            Integer operation = dayCalcFunction.apply(dateTime, day, update);
            update.setUpdateTime(updateTime);

            if (Objects.equals(operation, OperationStrategyEnum.CREATE.getType())) {
                measIndDayService.create(BeanUtil.copyProperties(update, MeasIndDayCreateReqVO.class));
            } else if (Objects.equals(operation, OperationStrategyEnum.UPDATE.getType())) {
                measIndDayService.update(update);
            }

        } catch (Exception e) {
            log.error("[processDayData][处理设备日数据异常] 设备ID: {}, 类型: {}, 异常: {}",
                    equip.getId(), equip.getDevType(), e.getMessage(), e);
        }
    }

    @Override
    public void processMonthData(EquipBaseCacheVO equip, List<MeasReportReceiveQueueMessage> list, LocalDateTime updateTime) {
        try {
            // 过滤需要清洗的数据
            List<MeasReportReceiveQueueMessage> filteredList = filterCleanData(MeasCleanTableTypeEnum.MONTH.getType(), equip, list);
            if (isEmpty(filteredList)) {
                return;
            }

            MeasReportReceiveQueueMessage msg = list.get(0);
            LocalDateTime dateTime = LocalDateTime.parse(msg.getDateTime(),
                    DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
            // 日期规则，00:08:01至第二日01:08:00为一天，所以减8小时1分钟
            LocalDateTime realtime = dateTime.minusHours(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).minusMinutes(1).withHour(0)
                    .withMinute(0);
            String convertTime = realtime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));

            MeasIndMonthDO month = measIndMonthService.getByEquipAndMonth(msg.getCentralId(), msg.getDevId(),
                    convertTime.substring(0, 7));

            MeasIndMonthUpdateReqVO update = new MeasIndMonthUpdateReqVO();
            BeanUtils.copyProperties(msg, update);
            update.setEquipId(equip.getId());
            update.setMonth(convertTime.substring(0, 7));
            update.setUpdateTime(updateTime);

            // 设置雨量
            filteredList.stream()
                    .filter(message -> {
                        Integer type = message.getVariableType();
                        return Objects.equals(type, MEAS_INDICATES_TYPE_RAIN_FIVE_MINUTE)
                                || Objects.equals(type, MEAS_INDICATES_TYPE_RAIN_HOUR);
                    })
                    .findFirst()
                    .ifPresent(message -> update.setRainfallVolume(message.getVariableValue()));

            if (Objects.isNull(month)) {
                measIndMonthService.create(BeanUtil.copyProperties(update, MeasIndMonthCreateReqVO.class));
            } else {
                update.setId(month.getId());
                update.setRainfallVolume(month.getRainfallVolume() + update.getRainfallVolume());
                measIndMonthService.update(update);
            }

        } catch (Exception e) {
            log.error("[processMonthData][处理设备月数据异常] 设备ID: {}, 类型: {}, 异常: {}",
                    equip.getId(), equip.getDevType(), e.getMessage(), e);
        }
    }

    @Override
    public void processYearData(EquipBaseCacheVO equip, List<MeasReportReceiveQueueMessage> list, LocalDateTime updateTime) {
        try {
            // 过滤需要清洗的数据
            List<MeasReportReceiveQueueMessage> filteredList = filterCleanData(MeasCleanTableTypeEnum.YEAR.getType(), equip, list);
            if (isEmpty(filteredList)) {
                return;
            }

            MeasReportReceiveQueueMessage msg = list.get(0);
            LocalDateTime dateTime = LocalDateTime.parse(msg.getDateTime(),
                    DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
            // 日期规则，00:08:01至第二日01:08:00为一天，所以减8小时1分钟
            LocalDateTime realtime = dateTime.minusHours(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).minusMinutes(1).withHour(0)
                    .withMinute(0);
            String convertTime = realtime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
            MeasIndYearDO year = measIndYearService.getByEquipAndYear(msg.getCentralId(), msg.getDevId(),
                    convertTime.substring(0, 4));

            MeasIndYearUpdateReqVO update = new MeasIndYearUpdateReqVO();
            BeanUtils.copyProperties(msg, update);
            update.setEquipId(equip.getId());
            update.setYear(convertTime.substring(0, 4));
            update.setUpdateTime(updateTime);

            // 设置雨量
            filteredList.stream()
                    .filter(message -> {
                        Integer type = message.getVariableType();
                        return Objects.equals(type, MEAS_INDICATES_TYPE_RAIN_FIVE_MINUTE)
                                || Objects.equals(type, MEAS_INDICATES_TYPE_RAIN_HOUR);
                    })
                    .findFirst()
                    .ifPresent(message -> update.setRainfallVolume(message.getVariableValue()));

            if (Objects.isNull(year)) {
                measIndYearService.create(BeanUtil.copyProperties(update, MeasIndYearCreateReqVO.class));
            } else {
                update.setId(year.getId());
                update.setRainfallVolume(year.getRainfallVolume() + update.getRainfallVolume());
                measIndYearService.update(update);
            }

        } catch (Exception e) {
            log.error("[processYearData][处理设备年数据异常] 设备ID: {}, 类型: {}, 异常: {}",
                    equip.getId(), equip.getDevType(), e.getMessage(), e);
        }
    }

    protected BiConsumer<EquipBaseCacheVO, MeasIndTimeCreateReqVO> timeCalcConsumer;

    protected void setTimeCalcConsumer(BiConsumer<EquipBaseCacheVO, MeasIndTimeCreateReqVO> timeCalcConsumer) {
        this.timeCalcConsumer = timeCalcConsumer;
    }

    protected Integer processHourWaterData(EquipBaseCacheVO equip, MeasIndHourUpdateReqVO update, Consumer<MeasIndHourUpdateReqVO> func) {
        // 水位计算
        LocalDateTime hourTime = update.getHourTime().withMinute(0).withSecond(0);
        MeasIndHourDO hourData = measIndhourService.getByEquipAndHour(equip.getCentralId(), equip.getDevId(), hourTime);

        if (Objects.isNull(hourData)) {
            update.setHourTime(hourTime);
            List<MeasIndTimeDO> lastHourDataList = measIndTimeService.getLastHourDataByEquipIdAndTime(equip.getCentralId(), equip.getDevId(), update.getHourTime());
            if (lastHourDataList.isEmpty()) {
                return OperationStrategyEnum.IGNORE.getType();
            } else if (lastHourDataList.get(0).getReportTime().getMinute() > 0
                    && lastHourDataList.get(0).getReportTime().getMinute() <= MEAS_WATER_LEVEL_HOUR_REPORT_TIMEOUT) {
                update.setHourTime(hourTime);
                update.setWaterLevel(0.0f);
                func.accept(update);
                return OperationStrategyEnum.CREATE.getType();
            } else {
                update.setHourTime(hourTime);
                update.setWaterLevel(lastHourDataList.get(0).getWaterLevel());
                update.setReservoirOutflow(lastHourDataList.get(0).getReservoirOutflow());
                func.accept(update);
                return OperationStrategyEnum.CREATE.getType();
            }
        } else {
            return OperationStrategyEnum.IGNORE.getType();
        }

    }

    protected BiFunction<EquipBaseCacheVO, MeasIndHourUpdateReqVO, Integer> hourCalcFunction;

    protected void setHourCalcFunction(BiFunction<EquipBaseCacheVO, MeasIndHourUpdateReqVO, Integer> hourCalcFunction) {
        this.hourCalcFunction = hourCalcFunction;
    }

    protected Integer processDayWaterData(LocalDateTime reportTime, MeasIndDayDO day, MeasIndDayUpdateReqVO update) {
        Pair<Float, Float> eightAndTwentyWaterLevel = baseDataCalcService.getEightAndTwentyWaterLevel(update.getCentralId(), update.getDevId(),
                reportTime);
        update.setEightWaterLevel(eightAndTwentyWaterLevel.getLeft());
        update.setTwentyWaterLevel(eightAndTwentyWaterLevel.getRight());

        List<MeasIndTimeDO> timeDataList = measIndTimeService.getListByEquipIdAndDayTime(update.getCentralId(),
                update.getDevId(), LocalDate.parse(update.getDay(), DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY)).atStartOfDay());

        if (Objects.isNull(day)) {
            update.setMaxWaterLevel(update.getMinWaterLevel());
            update.setAvgWaterLevel(update.getMinWaterLevel());
            setDayWaterValue(null, update, timeDataList);
            return OperationStrategyEnum.CREATE.getType();
        } else {
            update.setId(day.getId());
            update.setMaxWaterLevel(Math.max(day.getMaxWaterLevel(), update.getMinWaterLevel()));
            update.setMinWaterLevel(Math.min(day.getMinWaterLevel(), update.getMinWaterLevel()));

            // 平均水位，按时间表
            double avg = CollectionUtils.isEmpty(timeDataList) ? 0.0d :
                    timeDataList.stream().mapToDouble(MeasIndTimeDO::getWaterLevel).average().orElse(0.0d);
            update.setAvgWaterLevel(convert(avg, 3).floatValue());
            setDayWaterValue(day, update, timeDataList);
            return OperationStrategyEnum.UPDATE.getType();
        }
    }

    protected void setDayWaterValue(MeasIndDayDO day, MeasIndDayUpdateReqVO update, @NonNull List<MeasIndTimeDO> timeDataList) {
        DoubleSummaryStatistics stats = timeDataList.stream()
                .mapToDouble(MeasIndTimeDO::getCalcWaterFlow)
                .summaryStatistics();

        update.setMaxWaterFlow(convert(stats.getMax(), 3).floatValue());
        update.setMinWaterFlow(convert(stats.getMin(), 3).floatValue());
        update.setAvgWaterFlow(convert(stats.getAverage(), 3).floatValue());
        if (Objects.isNull(day)) {
            update.setWaterSupply(0.0f);
        } else {
            double waterVolume = timeDataList.stream().mapToDouble(MeasIndTimeDO::getCalcWaterVolume).sum();
            update.setWaterSupply((float) waterVolume);
        }
    }

    protected TriFunction<LocalDateTime, MeasIndDayDO, MeasIndDayUpdateReqVO, Integer> dayCalcFunction;

    protected void setDayCalcFunction(TriFunction<LocalDateTime, MeasIndDayDO, MeasIndDayUpdateReqVO, Integer> dayCalcFunction) {
        this.dayCalcFunction = dayCalcFunction;
    }

}
