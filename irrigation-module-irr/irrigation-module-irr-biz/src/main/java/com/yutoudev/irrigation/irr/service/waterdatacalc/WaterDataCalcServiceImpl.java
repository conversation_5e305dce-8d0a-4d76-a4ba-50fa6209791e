package com.yutoudev.irrigation.irr.service.waterdatacalc;

import com.yutoudev.irrigation.irr.calc.EquipFlowHelper;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindtime.MeasIndTimeCreateReqVO;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefLevelFlowDO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndTimeDO;
import com.yutoudev.irrigation.irr.service.coef.CoefLevelFlowService;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import com.yutoudev.irrigation.irr.service.measind.MeasIndTimeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.util.number.PrecisionUtils.convert;

/**
 * <AUTHOR>
 */
@Service
public class WaterDataCalcServiceImpl extends BaseDataCalcService implements WaterDataCalcService {

    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;

    @Resource
    private MeasIndTimeService<MeasIndTimeDO> measIndTimeService;

    @Resource
    private CoefLevelFlowService<CoefLevelFlowDO> coefLevelFlowService;

    @Resource
    private EquipFlowHelper equipFlowHelper;

    @Override
    public double getWaterFlowByLevel(EquipBaseCacheVO equip, double waterLevel) {
        return coefLevelFlowService.getFlowRate(equip.getId(), convert(waterLevel, 2));
    }

    @Override
    public double getWaterFlowByLevel(String centralId, String devId, double waterLevel) {
        EquipBaseCacheVO equip = equipBaseService.getCacheByCentralIdAndDevId(centralId, devId);
        return getWaterFlowByLevel(equip, waterLevel);
    }

    @Override
    public double getWaterFlowBySpeed(EquipBaseCacheVO equip, double waterLevel, double waterSpeed) {
        // 根据断面、速率获取流量
        equipFlowHelper.init(equip.getId(), convert(waterLevel, 3));
        return equipFlowHelper.getFlow(convert(waterSpeed, 3));
    }

    @Override
    public double getWaterFlowBySpeed(String centralId, String devId, double waterLevel, double waterSpeed) {
        EquipBaseCacheVO equip = equipBaseService.getCacheByCentralIdAndDevId(centralId, devId);
        return getWaterFlowBySpeed(equip, waterLevel, waterSpeed);
    }

    @Override
    public double getWaterSpeedByFlow(EquipBaseCacheVO equip, double waterLevel, double waterFlow) {
        equipFlowHelper.init(equip.getId(), convert(waterLevel, 3));
        return equipFlowHelper.getSpeedByFlow(convert(waterFlow, 3));
    }

    @Override
    public double getWaterVolumeByFlow(MeasIndTimeCreateReqVO create, double waterFlow) {
        // 水量 = 流量*时间
        MeasIndTimeDO timeData = measIndTimeService.getLastDataByEquipIdAndTime(create.getCentralId(),
                create.getDevId(), create.getReportTime());
        if (Objects.isNull(timeData)) {
            return 0;
        } else {
            // 计算时间秒差
            long between = ChronoUnit.SECONDS.between(timeData.getReportTime(), create.getReportTime());
            if (between > ChronoUnit.HOURS.getDuration().getSeconds()) {
                return 0;
            }
            waterFlow = convert(waterFlow, 3);
            return convert(new BigDecimal(waterFlow).multiply(new BigDecimal(between)), 3)
                    .doubleValue();
        }
    }
}
