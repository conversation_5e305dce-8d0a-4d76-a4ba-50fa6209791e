package com.yutoudev.irrigation.irr.dal.dataobject.measind;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;

import java.time.LocalDateTime;


/**
 * 量测设备指标统计小时DO
 *
 * <AUTHOR>
 * @description 管理后台-量测设备指标统计小时数据库对象
 * @time 2024-07-27 15:15:00
 */
@TableName(value = "irr_meas_ind_hour", autoResultMap = true)
@KeySequence("irr_meas_ind_hour_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeasIndHourDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 站点ID
     *
     */
    private Long equipId;

    /**
     * 中心站ID
     */
    private String centralId;

    /**
     * 遥测站ID
     */
    private String devId;

    /**
     * 小时降水量累计值
     */
    private Float rainfallVolume;

    /**
     * 小时水位
     */
    private Float waterLevel;

    /**
     * 入库流量
     */
    private Float reservoirInflow;

    /**
     * 出库流量
     */
    private Float reservoirOutflow;

    /**
     * 水库容量差
     */
    private Float reservoirCapacityDiff;

    /**
     * 来水量
     */
    private Float reservoirInflowVolume;

    /**
     * 电压
     */
    private Float voltage;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 小时时间
     */
    private LocalDateTime hourTime;

    @TableField(exist = false)
    private LocalDateTime createTime;

    @TableField(exist = false)
    private String creator;

    @TableField(exist = false)
    private String updater;

    @TableField(exist = false)
    private Boolean deleted;

    /**
     * 小时降水量累计值-累加值
     */
    @TableField(exist = false)
    private Float rainfallVolumeCumulative;
}
