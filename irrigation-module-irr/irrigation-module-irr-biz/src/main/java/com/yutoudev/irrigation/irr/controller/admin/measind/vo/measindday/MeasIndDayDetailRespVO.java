package com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindday;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 *
 * 量测设备指标统计日DetailResponseVO
 * @description 管理后台-量测设备指标统计日DetailResponseVO
 * <AUTHOR>
 * @time 2024-06-16 20:28:51
 *
 */
@Data
@ToString(callSuper = true)
public class MeasIndDayDetailRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 站点ID
     *
     */
    private Long equipId;

    /**
     * 中心站ID
     * 
     */
    private String centralId;

    /**
     * 遥测站ID
     * 
     */
    private String devId;

    /**
     * 日期
     * 
     */
    private String day;

    /**
     * 8点水位
     *
     */
    private Float eightWaterLevel;

    /**
     * 20点水位
     *
     */
    private Float twentyWaterLevel;

    /**
     * 日最高水位
     *
     */
    private Float maxWaterLevel;

    /**
     * 日最低水位
     *
     */
    private Float minWaterLevel;

    /**
     * 日平均水位
     *
     */
    private Float avgWaterLevel;

    /**
     * 日最大流量
     *
     */
    private Float maxWaterFlow;

    /**
     * 日最小流量
     *
     */
    private Float minWaterFlow;

    /**
     * 日平均流量
     *
     */
    private Float avgWaterFlow;

    /**
     * 日入库水量
     *
     */
    private Float waterVolume;

    /**
     * 日供水量
     *
     */
    private Float waterSupply;

    /**
     * 日降水量累计值
     *
     */
    private Float rainfallVolume;

    /**
     * 更新时间
     *
     */
    private LocalDateTime updateTime;
}
